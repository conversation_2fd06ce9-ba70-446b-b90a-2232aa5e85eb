/**
 * ATMA Backend - Docker Load Testing Script
 * Tests the performance and scalability of the Docker deployment
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

// Configuration
const config = {
  baseURL: 'http://localhost:3000',
  timeout: 30000,
  loadTest: {
    concurrentUsers: 10,
    requestsPerUser: 5,
    rampUpTime: 2000, // ms
    testDuration: 30000 // ms
  }
};

// Test results
let loadTestResults = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  averageResponseTime: 0,
  minResponseTime: Infinity,
  maxResponseTime: 0,
  responseTimes: [],
  errors: []
};

// Helper function to create test user
function createTestUser(index) {
  return {
    username: `loadtest-user-${index}`,
    email: `loadtest-${index}@example.com`,
    password: 'LoadTest123!',
    fullName: `Load Test User ${index}`
  };
}

// Helper function to make timed API request
async function timedRequest(method, endpoint, data = null, headers = {}) {
  const startTime = performance.now();
  
  try {
    const response = await axios({
      method,
      url: `${config.baseURL}${endpoint}`,
      data,
      headers,
      timeout: config.timeout,
      validateStatus: () => true
    });
    
    const endTime = performance.now();
    const responseTime = endTime - startTime;
    
    return {
      success: response.status >= 200 && response.status < 300,
      status: response.status,
      responseTime,
      data: response.data
    };
  } catch (error) {
    const endTime = performance.now();
    const responseTime = endTime - startTime;
    
    return {
      success: false,
      status: 0,
      responseTime,
      error: error.message
    };
  }
}

// Simulate user workflow
async function simulateUserWorkflow(userIndex) {
  const user = createTestUser(userIndex);
  const results = [];
  
  console.log(`👤 User ${userIndex}: Starting workflow`);
  
  try {
    // Step 1: Register user
    const registerResult = await timedRequest('POST', '/auth/register', user);
    results.push({ step: 'register', ...registerResult });
    
    // Step 2: Login
    const loginResult = await timedRequest('POST', '/auth/login', {
      email: user.email,
      password: user.password
    });
    results.push({ step: 'login', ...loginResult });
    
    let authToken = null;
    if (loginResult.success && loginResult.data?.data?.token) {
      authToken = loginResult.data.data.token;
    }
    
    if (authToken) {
      // Step 3: Get profile
      const profileResult = await timedRequest('GET', '/auth/profile', null, {
        'Authorization': `Bearer ${authToken}`
      });
      results.push({ step: 'profile', ...profileResult });
      
      // Step 4: Submit assessment
      const assessmentData = {
        assessmentName: `Load Test Assessment ${userIndex}`,
        candidateName: `Test Candidate ${userIndex}`,
        candidateEmail: `candidate-${userIndex}@test.com`,
        responses: [
          {
            question: 'What is your experience with load testing?',
            answer: 'I have experience with performance testing and optimization.'
          }
        ]
      };
      
      const assessmentResult = await timedRequest('POST', '/assessment/submit', assessmentData, {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      });
      results.push({ step: 'assessment', ...assessmentResult });
      
      // Step 5: Check archive
      const archiveResult = await timedRequest('GET', '/archive/results?page=1&limit=5', null, {
        'Authorization': `Bearer ${authToken}`
      });
      results.push({ step: 'archive', ...archiveResult });
    }
    
    console.log(`👤 User ${userIndex}: Completed workflow`);
    return results;
    
  } catch (error) {
    console.log(`👤 User ${userIndex}: Error in workflow - ${error.message}`);
    return results;
  }
}

// Process test results
function processResults(allResults) {
  loadTestResults.totalRequests = 0;
  loadTestResults.successfulRequests = 0;
  loadTestResults.failedRequests = 0;
  loadTestResults.responseTimes = [];
  loadTestResults.errors = [];
  
  allResults.forEach((userResults, userIndex) => {
    userResults.forEach(result => {
      loadTestResults.totalRequests++;
      loadTestResults.responseTimes.push(result.responseTime);
      
      if (result.success) {
        loadTestResults.successfulRequests++;
      } else {
        loadTestResults.failedRequests++;
        loadTestResults.errors.push({
          user: userIndex,
          step: result.step,
          status: result.status,
          error: result.error
        });
      }
      
      // Update min/max response times
      if (result.responseTime < loadTestResults.minResponseTime) {
        loadTestResults.minResponseTime = result.responseTime;
      }
      if (result.responseTime > loadTestResults.maxResponseTime) {
        loadTestResults.maxResponseTime = result.responseTime;
      }
    });
  });
  
  // Calculate average response time
  if (loadTestResults.responseTimes.length > 0) {
    loadTestResults.averageResponseTime = 
      loadTestResults.responseTimes.reduce((a, b) => a + b, 0) / loadTestResults.responseTimes.length;
  }
  
  // Calculate percentiles
  const sortedTimes = loadTestResults.responseTimes.sort((a, b) => a - b);
  const p50Index = Math.floor(sortedTimes.length * 0.5);
  const p95Index = Math.floor(sortedTimes.length * 0.95);
  const p99Index = Math.floor(sortedTimes.length * 0.99);
  
  loadTestResults.p50ResponseTime = sortedTimes[p50Index] || 0;
  loadTestResults.p95ResponseTime = sortedTimes[p95Index] || 0;
  loadTestResults.p99ResponseTime = sortedTimes[p99Index] || 0;
}

// Main load test function
async function runLoadTest() {
  console.log('🚀 ATMA Backend Docker Load Testing');
  console.log('='.repeat(60));
  console.log(`Target: ${config.baseURL}`);
  console.log(`Concurrent Users: ${config.loadTest.concurrentUsers}`);
  console.log(`Requests per User: ${config.loadTest.requestsPerUser}`);
  console.log(`Ramp-up Time: ${config.loadTest.rampUpTime}ms`);
  console.log('='.repeat(60));
  
  const startTime = performance.now();
  
  // Create promises for all users
  const userPromises = [];
  
  for (let i = 0; i < config.loadTest.concurrentUsers; i++) {
    // Stagger user start times for ramp-up
    const delay = (i * config.loadTest.rampUpTime) / config.loadTest.concurrentUsers;
    
    const userPromise = new Promise(resolve => {
      setTimeout(async () => {
        const results = await simulateUserWorkflow(i + 1);
        resolve(results);
      }, delay);
    });
    
    userPromises.push(userPromise);
  }
  
  console.log('\n📊 Running load test...');
  
  // Wait for all users to complete
  const allResults = await Promise.all(userPromises);
  
  const endTime = performance.now();
  const totalTestTime = endTime - startTime;
  
  // Process and display results
  processResults(allResults);
  
  console.log('\n' + '='.repeat(60));
  console.log('📈 LOAD TEST RESULTS');
  console.log('='.repeat(60));
  console.log(`Total Test Time: ${(totalTestTime / 1000).toFixed(2)}s`);
  console.log(`Total Requests: ${loadTestResults.totalRequests}`);
  console.log(`Successful Requests: ${loadTestResults.successfulRequests}`);
  console.log(`Failed Requests: ${loadTestResults.failedRequests}`);
  console.log(`Success Rate: ${((loadTestResults.successfulRequests / loadTestResults.totalRequests) * 100).toFixed(2)}%`);
  console.log(`Requests per Second: ${(loadTestResults.totalRequests / (totalTestTime / 1000)).toFixed(2)}`);
  
  console.log('\n📊 Response Time Statistics:');
  console.log(`Average: ${loadTestResults.averageResponseTime.toFixed(2)}ms`);
  console.log(`Minimum: ${loadTestResults.minResponseTime.toFixed(2)}ms`);
  console.log(`Maximum: ${loadTestResults.maxResponseTime.toFixed(2)}ms`);
  console.log(`50th Percentile: ${loadTestResults.p50ResponseTime.toFixed(2)}ms`);
  console.log(`95th Percentile: ${loadTestResults.p95ResponseTime.toFixed(2)}ms`);
  console.log(`99th Percentile: ${loadTestResults.p99ResponseTime.toFixed(2)}ms`);
  
  if (loadTestResults.errors.length > 0) {
    console.log('\n❌ Errors:');
    loadTestResults.errors.forEach(error => {
      console.log(`  User ${error.user}, Step ${error.step}: Status ${error.status} - ${error.error || 'Unknown error'}`);
    });
  }
  
  // Performance assessment
  console.log('\n🎯 Performance Assessment:');
  const avgResponseTime = loadTestResults.averageResponseTime;
  const successRate = (loadTestResults.successfulRequests / loadTestResults.totalRequests) * 100;
  
  if (successRate >= 99 && avgResponseTime < 500) {
    console.log('🟢 EXCELLENT: System performance is excellent');
  } else if (successRate >= 95 && avgResponseTime < 1000) {
    console.log('🟡 GOOD: System performance is acceptable');
  } else if (successRate >= 90 && avgResponseTime < 2000) {
    console.log('🟠 FAIR: System performance needs improvement');
  } else {
    console.log('🔴 POOR: System performance is inadequate');
  }
  
  console.log('\n💡 Recommendations:');
  if (avgResponseTime > 1000) {
    console.log('  - Consider optimizing database queries');
    console.log('  - Check if services have sufficient resources');
  }
  if (successRate < 95) {
    console.log('  - Investigate error causes');
    console.log('  - Consider scaling up infrastructure');
  }
  if (loadTestResults.maxResponseTime > 5000) {
    console.log('  - Some requests are very slow - check for bottlenecks');
  }
  
  return loadTestResults;
}

// Run load test if this file is executed directly
if (require.main === module) {
  runLoadTest()
    .then(() => {
      process.exit(loadTestResults.failedRequests === 0 ? 0 : 1);
    })
    .catch(error => {
      console.error('\n💥 Load test error:', error.message);
      process.exit(1);
    });
}

module.exports = { runLoadTest, loadTestResults };
